import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/ui/theme-provider" // Assuming common UI components in ui folder
import { BoardProvider } from "@/components/kanban/board-context"
import { AgentBoardControllerProvider } from "@/components/kanban/agent-board-controller"
import { Toaster } from "@/components/ui/toaster" // Assuming common UI components in ui folder
import { SearchProvider } from "@/components/search-provider" // SearchProvider is now a global component
import { Suspense } from "react"
import { ClientLayout } from "@/components/client-layout" // Client component wrapper

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Synapse - AI-Powered Development Environment",
  description: "A comprehensive AI-assisted software development environment with Kanban task management and intelligent agents.",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem storageKey="synapse-theme">
          <SearchProvider>
            <BoardProvider>
              <AgentBoardControllerProvider>
                <ClientLayout>
                  <Suspense>{children}</Suspense>
                </ClientLayout>
                <Toaster />
              </AgentBoardControllerProvider>
            </BoardProvider>
          </SearchProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
