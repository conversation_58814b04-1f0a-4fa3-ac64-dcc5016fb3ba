"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/header"
import { AgentActivityPanel } from "@/components/kanban/agent-activity-panel"

interface ClientLayoutProps {
  children: React.ReactNode
}

export function ClientLayout({ children }: ClientLayoutProps) {
  // Local state for header to control AgentActivityPanel visibility
  const [showAgentPanel, setShowAgentPanel] = useState(false)

  return (
    <>
      <Header 
        showAgentPanel={showAgentPanel} 
        toggleAgentPanel={() => setShowAgentPanel(!showAgentPanel)} 
      />
      <div className="flex flex-1 overflow-hidden h-[calc(100vh-3.5rem)]">
        {children}
        {showAgentPanel && (
          <div className="h-full">
            <AgentActivityPanel />
          </div>
        )}
      </div>
    </>
  )
}
