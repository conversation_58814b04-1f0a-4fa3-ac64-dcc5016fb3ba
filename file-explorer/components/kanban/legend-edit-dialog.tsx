"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { PlusCircle, Trash2 } from "lucide-react"
import { CardType } from "./board-context" // Assuming CardType is available via board-context

interface LegendEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cardTypes: CardType[]
  onSave: (updatedCardTypes: CardType[]) => void
}

export function LegendEditDialog({ open, onOpenChange, cardTypes, onSave }: LegendEditDialogProps) {
  const [localCardTypes, setLocalCardTypes] = useState<CardType[]>([])
  const [newTypeName, setNewTypeName] = useState("")
  const [newTypeColor, setNewTypeColor] = useState("#888888")

  useEffect(() => {
    // Always run the effect, but only update when dialog is opened
    if (open) {
      setLocalCardTypes(cardTypes)
    }
  }, [open, cardTypes])

  const handleTypeChange = (id: string, field: keyof CardType, value: string) => {
    setLocalCardTypes((prev) =>
      prev.map((type) => (type.id === id ? { ...type, [field]: value } : type))
    )
  }

  const handleAddType = () => {
    if (newTypeName.trim()) {
      const newId = newTypeName.toLowerCase().replace(/\s+/g, "-")
      setLocalCardTypes((prev) => [
        ...prev,
        { id: newId, name: newTypeName.trim(), color: newTypeColor },
      ])
      setNewTypeName("")
      setNewTypeColor("#888888")
    }
  }

  const handleRemoveType = (id: string) => {
    setLocalCardTypes((prev) => prev.filter((type) => type.id !== id))
  }

  const handleSave = () => {
    onSave(localCardTypes)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Kanban Legend</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <h4 className="text-sm font-medium mb-2">Configure Card Types</h4>
          <div className="space-y-4">
            {localCardTypes.map((type) => (
              <div key={type.id} className="flex items-end gap-2">
                <div className="flex-1 grid gap-1">
                  <Label htmlFor={`type-name-${type.id}`} className="sr-only">Name</Label>
                  <Input
                    id={`type-name-${type.id}`}
                    value={type.name}
                    onChange={(e) => handleTypeChange(type.id, "name", e.target.value)}
                    placeholder="Type Name"
                  />
                </div>
                <div className="grid gap-1">
                  <Label htmlFor={`type-color-${type.id}`} className="sr-only">Color</Label>
                  <Input
                    id={`type-color-${type.id}`}
                    type="color"
                    value={type.color}
                    onChange={(e) => handleTypeChange(type.id, "color", e.target.value)}
                    className="h-10 w-10 p-0"
                  />
                </div>
                <Button variant="ghost" size="icon" onClick={() => handleRemoveType(type.id)}>
                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                </Button>
              </div>
            ))}
          </div>

          <div className="flex items-end gap-2 mt-4">
            <div className="flex-1 grid gap-1">
              <Label htmlFor="new-type-name">New Type Name</Label>
              <Input
                id="new-type-name"
                value={newTypeName}
                onChange={(e) => setNewTypeName(e.target.value)}
                placeholder="e.g., Epic"
              />
            </div>
            <div className="grid gap-1">
              <Label htmlFor="new-type-color">Color</Label>
              <Input
                id="new-type-color"
                type="color"
                value={newTypeColor}
                onChange={(e) => setNewTypeColor(e.target.value)}
                className="h-10 w-10 p-0"
              />
            </div>
            <Button onClick={handleAddType} size="icon" disabled={!newTypeName.trim()}>
              <PlusCircle className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleSave}>
            Save Legend
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}