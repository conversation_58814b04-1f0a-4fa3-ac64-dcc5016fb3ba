"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useAgentBoardController } from "./agent-board-controller"
import { useBoard } from "./board-context"
import { X, Play, Pause, Trash2, RefreshCw, ChevronRight, ChevronDown, Cpu, Code, FileText, TestTube, Settings, AlertTriangle } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Agent } from "./agent-board-controller"

export function AgentActivityPanel() {
  const [activeTab, setActiveTab] = useState("activity")
  const [expandedAgents, setExpandedAgents] = useState<Record<string, boolean>>({})

  const { agentLogs, clearLogs, agentStatus, isAgentRunning, startAgent, stopAgent, agents } = useAgentBoardController();
  const { activeBoard } = useBoard();

  const toggleAgentExpansion = (agentId: string) => {
    setExpandedAgents((prev) => ({
      ...prev,
      [agentId]: !prev[agentId],
    }))
  }

  const getAgentIcon = (type: Agent['type']) => {
    switch (type) {
      case 'orchestrator':
        return <Cpu className="h-6 w-6 text-indigo-500" />;
      case 'implementation':
        return <Code className="h-6 w-6 text-green-500" />;
      case 'specialized':
        return <FileText className="h-6 w-6 text-blue-500" />;
      case 'middleware':
        return <Settings className="h-6 w-6 text-gray-500" />;
      default:
        return <Cpu className="h-6 w-6 text-gray-500" />;
    }
  }

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case "busy":
        return "bg-green-500"
      case "idle":
        return "bg-blue-500"
      case "paused":
        return "bg-yellow-500"
      case "error":
        return "bg-red-500"
      case "offline":
        return "bg-gray-500";
      default:
        return "bg-gray-500"
    }
  }

  const filteredAgents = activeBoard?.agents.filter((agent) => {
    if (activeTab === "all") return true
    if (activeTab === "working") return agent.status === "busy"
    if (activeTab === "idle") return agent.status === "idle"
    if (activeTab === "paused") return agent.status === "paused"
    if (activeTab === "error") return agent.status === "error"
    return true
  }) || [];

  return (
    <div className="w-80 border-l border-border bg-background flex flex-col h-full">
      <div className="flex items-center justify-between p-3 border-b border-border">
        <h3 className="font-medium">Agent Activity</h3>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <X className="h-4 w-4" />
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="mx-3 mt-2 grid w-auto grid-cols-4">
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="error">Errors</TabsTrigger>
        </TabsList>

        <TabsContent value="activity" className="flex-1 flex flex-col p-0">
          <div className="p-3 border-b border-border flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className={`w-2 h-2 rounded-full ${
                  agentStatus === "running"
                    ? "bg-green-500"
                    : agentStatus === "paused"
                    ? "bg-yellow-500"
                    : agentStatus === "error"
                    ? "bg-red-500"
                    : "bg-gray-500"
                }`}
              />
              <span className="text-sm">
                {agentStatus === "running"
                  ? "Running"
                  : agentStatus === "paused"
                  ? "Paused"
                  : agentStatus === "error"
                  ? "Error"
                  : "Idle"}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={isAgentRunning ? stopAgent : startAgent}
                title={isAgentRunning ? "Pause Agent" : "Start Agent"}
              >
                {isAgentRunning ? <Pause className="h-3.5 w-3.5" /> : <Play className="h-3.5 w-3.5" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={clearLogs}
                title="Clear Logs"
              >
                <Trash2 className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          <ScrollArea className="flex-1 p-3">
            <div className="space-y-2">
              {agentLogs.length > 0 ? (
                agentLogs.map((log, index) => (
                  <div key={index} className="text-sm border-l-2 border-primary pl-2 py-1">
                    {log}
                  </div>
                ))
              ) : (
                <div className="text-sm text-muted-foreground text-center py-8">
                  No agent activity yet. Start the agent to see logs.
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="agents" className="flex-1 flex flex-col p-0">
          <div className="p-3 border-b border-border flex items-center justify-between">
            <span className="text-sm font-medium">Active Agents</span>
            <Button variant="ghost" size="icon" className="h-7 w-7" title="Refresh">
              <RefreshCw className="h-3.5 w-3.5" />
            </Button>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-3 space-y-3">
              {activeBoard?.agents && activeBoard.agents.length > 0 ? (
                activeBoard.agents.map((agent) => (
                  <div key={agent.id} className="border border-border rounded-md overflow-hidden">
                    <div
                      className="flex items-center justify-between p-2 bg-muted/50 cursor-pointer"
                      onClick={() => toggleAgentExpansion(agent.id)}
                    >
                      <div className="flex items-center gap-2">
                        {expandedAgents[agent.id] ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                        <span className="font-medium">{agent.name}</span>
                        <span
                          className={`text-xs px-1.5 py-0.5 rounded-full ${
                            agent.status === "active"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                          }`}
                        >
                          {agent.status}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">{agent.type}</div>
                    </div>

                    {expandedAgents[agent.id] && (
                      <div className="p-3 text-sm space-y-2">
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Health Score (CPU)</div>
                          <Progress value={agent.healthScore} className="h-2" />
                          <span className="text-xs text-muted-foreground">{agent.healthScore.toFixed(1)}%</span>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Memory Usage (Placeholder)</div>
                          <Progress value={Math.min(100, (agent.tokensUsed / 1000) * 0.1)} className="h-2" />
                          <span className="text-xs text-muted-foreground">~{(agent.tokensUsed / 1000).toFixed(1)}k tokens</span>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Tokens Used</div>
                          <Progress value={Math.min(100, (agent.tokensUsed / 10000))} className="h-2" />
                          <span className="text-xs text-muted-foreground">{agent.tokensUsed.toLocaleString()}</span>
                        </div>
                        <div className="pt-2">
                          <div className="text-xs text-muted-foreground mb-1">Capabilities</div>
                          <div className="flex flex-wrap gap-1">
                            {agent.capabilities.map((capability, index) => (
                              <span
                                key={index}
                                className="text-xs px-1.5 py-0.5 bg-muted rounded-full"
                              >
                                {capability}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-sm text-muted-foreground text-center py-8">
                  No agents configured. Add agents in the board settings.
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="settings" className="flex-1 p-3">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Agent Settings</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-start agents</span>
                  <div className="h-4 w-8 bg-muted rounded-full relative">
                    <div className="absolute right-0.5 top-0.5 h-3 w-3 rounded-full bg-primary" />
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Agent notifications</span>
                  <div className="h-4 w-8 bg-primary rounded-full relative">
                    <div className="absolute right-0.5 top-0.5 h-3 w-3 rounded-full bg-background" />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Resource Limits</h4>
              <div className="space-y-3">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs">CPU Limit</span>
                    <span className="text-xs">80%</span>
                  </div>
                  <Progress value={80} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs">Memory Limit</span>
                    <span className="text-xs">60%</span>
                  </div>
                  <Progress value={60} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs">Token Limit</span>
                    <span className="text-xs">50%</span>
                  </div>
                  <Progress value={50} className="h-2" />
                </div>
              </div>
            </div>

            <Button className="w-full">Save Settings</Button>
          </div>
        </TabsContent>

        <TabsContent value="error" className="flex-1 flex flex-col p-0">
          <div className="p-3 border-b border-border flex items-center justify-between">
            <span className="text-sm font-medium text-destructive">Error Log</span>
            <Button variant="ghost" size="icon" className="h-7 w-7" onClick={clearLogs} title="Clear Error Logs">
              <Trash2 className="h-3.5 w-3.5 text-destructive" />
            </Button>
          </div>
          <ScrollArea className="flex-1 p-3">
            <div className="space-y-2">
              {agentLogs.filter(log => log.includes('[error]')).length > 0 ? (
                agentLogs.filter(log => log.includes('[error]')).map((log, index) => (
                  <div key={index} className="text-sm border-l-2 border-red-500 pl-2 py-1 text-red-400">
                    <AlertTriangle className="inline-block h-3 w-3 mr-1" />{log}
                  </div>
                ))
              ) : (
                <div className="text-sm text-muted-foreground text-center py-8">
                  No agent errors reported.
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}